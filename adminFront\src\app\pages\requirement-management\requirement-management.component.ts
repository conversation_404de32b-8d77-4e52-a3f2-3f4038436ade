import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { EnumHelper } from 'src/app/shared/helper/enumHelper';
import { PetternHelper } from 'src/app/shared/helper/petternHelper';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, RequirementService } from 'src/services/api/services';
import { BaseComponent } from '../components/base/baseComponent';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';
import { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';
import { FormsModule } from '@angular/forms';
import { NgFor, NgIf } from '@angular/common';
import { PaginationComponent } from '../components/pagination/pagination.component';
import { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';
import { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';
import { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';

@Component({
  selector: 'app-requirement-management',
  standalone: true, imports: [
    NbCardModule,
    BreadcrumbComponent,
    NbInputModule,
    FormsModule,
    NbSelectModule,
    NbOptionModule,
    NgIf,
    NgFor,
    PaginationComponent,
    StatusPipe,
    NbCheckboxModule,
    FormGroupComponent,
    NumberWithCommasPipe,
    NbTabsetModule,
    TemplateViewerComponent
  ],
  templateUrl: './requirement-management.component.html',
  styleUrl: './requirement-management.component.scss'
})
export class RequirementManagementComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private enumHelper: EnumHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private buildCaseService: BuildCaseService,
    private requirementService: RequirementService,
    private pettern: PetternHelper,
    private router: Router,
    private destroyref: DestroyRef
  ) {
    super(_allow);
    this.initializeSearchForm();
    this.getBuildCaseList();
  }
  // request
  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };
  getRequirementRequest: GetRequirementByIdRequest = {};
  // response
  buildCaseList: BuildCaseGetListReponse[] = [];
  requirementList: GetRequirement[] = [];
  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };

  statusOptions = [
    { value: 0, label: '停用' },
    { value: 1, label: '啟用' },
  ];
  houseType = this.enumHelper.getEnumOptions(EnumHouseType);
  isNew = false;
  currentBuildCase = 0;
  currentTab = 0; // 追蹤當前選中的 tab



  override ngOnInit(): void { }
  // 初始化搜尋表單
  initializeSearchForm() {
    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1
    this.getListRequirementRequest.CStatus = -1;
    this.getListRequirementRequest.CIsShow = null;
    this.getListRequirementRequest.CRequirement = '';
    this.getListRequirementRequest.CGroupName = '';
    // 預設全選所有房屋類型
    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);
  }

  // 重置搜尋
  resetSearch() {
    this.initializeSearchForm();
    // 重置後如果有建案資料，重新設定預設選擇第一個建案
    if (this.buildCaseList && this.buildCaseList.length > 0) {
      setTimeout(() => {
        if (this.currentTab === 0) {
          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
        } else {
          this.getListRequirementRequest.CBuildCaseID = 0;
        }
        this.getList();
      }, 0);
    } else {
      this.getList();
    }
  }

  getHouseType(hTypes: number[] | number | null | undefined): string {
    if (!hTypes) {
      return '';
    }

    if (!Array.isArray(hTypes)) {
      hTypes = [hTypes];
    }

    let labels: string[] = [];
    hTypes.forEach(htype => {
      let findH = this.houseType.find(x => x.value == htype);
      if (findH) {
        labels.push(findH.label);
      }
    });
    return labels.join(', ');
  } validation() {
    this.valid.clear();

    // 根據當前 tab 決定是否需要驗證建案名稱
    if (this.currentTab === 0) {
      // 建案頁面需要驗證建案名稱
      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);
    }

    this.valid.required('[需求]', this.saveRequirement.CRequirement);
    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);
    this.valid.required('[安排]', this.saveRequirement.CSort);
    this.valid.required('[狀態]', this.saveRequirement.CStatus);
    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);
    this.valid.required('[單位]', this.saveRequirement.CUnit);

    // 群組名稱長度驗證
    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {
      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');
    }

    // 備註說明長度驗證
    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {
      this.valid.errorMessages.push('[備註說明] 不能超過100個字');
    }
  }

  add(dialog: TemplateRef<any>) {
    this.isNew = true;
    this.saveRequirement = { CHouseType: [], CIsShow: false };
    this.saveRequirement.CStatus = 1;
    this.saveRequirement.CUnitPrice = 0;

    // 根據當前 tab 決定是否需要建案ID
    if (this.currentTab === 0) {
      // 建案頁面 - 使用當前選擇的建案或第一個建案
      if (this.currentBuildCase != 0) {
        this.saveRequirement.CBuildCaseID = this.currentBuildCase;
      } else if (this.buildCaseList && this.buildCaseList.length > 0) {
        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;
      }
    } else {
      // 模板頁面 - 設定建案ID為0
      this.saveRequirement.CBuildCaseID = 0;
    }

    this.dialogService.open(dialog);
  }

  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {
    this.getRequirementRequest.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    try {
      await this.getData();
      this.dialogService.open(dialog);
    } catch (error) {
      console.log("Failed to get function data", error)
    }
  }

  save(ref: any) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }

    // 如果是模板頁面，確保 CBuildCaseID 設定為 0
    if (this.currentTab === 1) {
      this.saveRequirement.CBuildCaseID = 0;
    }

    this.requirementService.apiRequirementSaveDataPost$Json({
      body: this.saveRequirement
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG('執行成功');
        this.getList();
      } else {
        this.message.showErrorMSG(res.Message!)
      }
    });
    ref.close();
  }

  onDelete(data: GetRequirement) {
    this.saveRequirement.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    if (window.confirm('是否確定刪除?')) {
      this.remove();
    } else {
      return;
    }
  }

  remove() {
    this.requirementService.apiRequirementDeleteDataPost$Json({
      body: {
        CRequirementID: this.saveRequirement.CRequirementID!
      }
    }).subscribe(res => {
      this.message.showSucessMSG('執行成功');
      this.getList();
    });
  }

  getBuildCaseList() {
    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })
      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {
        this.buildCaseList = res.Entries!;
        // 只在建案 tab 下且有建案時才查詢
        if (this.currentTab === 0 && this.buildCaseList.length > 0) {
          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
          this.getList();
        } else if (this.currentTab === 1) {
          this.getListRequirementRequest.CBuildCaseID = 0;
          this.getList();
        }
      })
  }

  getList() {
    this.getListRequirementRequest.PageSize = this.pageSize;
    this.getListRequirementRequest.PageIndex = this.pageIndex;
    this.requirementList = [] as GetRequirement[];
    this.totalRecords = 0;
    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動
    if (this.currentTab === 1) {
      this.getListRequirementRequest.CBuildCaseID = 0;
    } else {
      // 建案頁面的邏輯保持不變
      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {
        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;
      }
    }

    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.requirementList = res.Entries;
            this.totalRecords = res.TotalItems!;
          }
        }
      })
  } getData() {
    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.saveRequirement = { CHouseType: [], CIsShow: false };
            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;
            this.saveRequirement.CGroupName = res.Entries.CGroupName;
            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];
            this.saveRequirement.CRemark = res.Entries.CRemark;
            this.saveRequirement.CRequirement = res.Entries.CRequirement;
            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;
            this.saveRequirement.CSort = res.Entries.CSort;
            this.saveRequirement.CStatus = res.Entries.CStatus;
            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;
            this.saveRequirement.CUnit = res.Entries.CUnit;
            // TODO: 等後端API更新後啟用這行
            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;
          }
        }
      })
  }

  onHouseTypeChange(value: number, checked: any) {
    console.log(checked);

    if (checked) {
      if (!this.saveRequirement.CHouseType?.includes(value)) {
        this.saveRequirement.CHouseType?.push(value);
      }
      console.log(this.saveRequirement.CHouseType);
    } else {
      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);
    }
  }

  getCIsShowText(data: any): string {
    return data.CIsShow ? '是' : '否';
  }

  // Tab 切換事件處理
  private isFirstTabChange = true;
  onTabChange(event: any) {
    // 避免頁面初始化時自動觸發重複查詢
    if (this.isFirstTabChange) {
      this.isFirstTabChange = false;
      return;
    }
    // 根據 tabTitle 來判斷當前頁面
    if (event.tabTitle === '共用') {
      this.currentTab = 1;
      this.getListRequirementRequest.CBuildCaseID = 0;
    } else {
      this.currentTab = 0;
      // 切換回建案頁面時，如果有建案資料，預設選擇第一個
      if (this.buildCaseList && this.buildCaseList.length > 0) {
        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
      }
    }
    this.getList();
  }

  // 新增模板
  addTemplate(dialog: TemplateRef<any>) {
    this.isNew = true;
    this.saveRequirement = { CHouseType: [], CIsShow: false };
    this.saveRequirement.CStatus = 1;
    this.saveRequirement.CUnitPrice = 0;
    // 模板設定建案ID為0
    this.saveRequirement.CBuildCaseID = 0;
    this.dialogService.open(dialog);
  }

  // 編輯模板
  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {
    this.getRequirementRequest.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    try {
      await this.getData();
      this.dialogService.open(dialog);
    } catch (error) {
      console.log("Failed to get template data", error);
    }
  }

  // 保存模板
  saveTemplate(ref: any) {
    // 模板驗證（不包含建案名稱）
    this.valid.clear();
    this.valid.required('[需求]', this.saveRequirement.CRequirement);
    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);
    this.valid.required('[安排]', this.saveRequirement.CSort);
    this.valid.required('[狀態]', this.saveRequirement.CStatus);
    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);
    this.valid.required('[單位]', this.saveRequirement.CUnit);

    // 群組名稱長度驗證
    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {
      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');
    }

    // 備註說明長度驗證
    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {
      this.valid.errorMessages.push('[備註說明] 不能超過100個字');
    }

    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }

    // 確保模板建案ID為0
    const templateData = { ...this.saveRequirement };
    templateData.CBuildCaseID = 0;

    this.requirementService.apiRequirementSaveDataPost$Json({
      body: templateData
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG('執行成功');
        this.getList();
      } else {
        this.message.showErrorMSG(res.Message!)
      }
    });
    ref.close();
  }

  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {
    this.dialogService.open(templateViewerDialog);
  }

  onSelectTemplate(tpl: Template) {
    // 查看模板邏輯
    alert('查看模板: ' + tpl.TemplateName);
  }
}
